<?= $this->extend('layouts/applicant_layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">Edit File Information</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/profile#files') ?>">Profile</a></li>
                            <li class="breadcrumb-item active">Edit File</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>

            <!-- Edit Form Card -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>Edit File Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Display validation errors -->
                            <?php if (session()->getFlashdata('errors')): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Display success message -->
                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('success') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Display error message -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Current File Info -->
                            <div class="alert alert-info">
                                <h6 class="alert-heading">Current File:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>File:</strong> <?= esc($file['file_title']) ?><br>
                                        <strong>Uploaded:</strong> <?= date('M d, Y g:i A', strtotime($file['created_at'])) ?>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="<?= base_url($file['file_path']) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-eye me-1"></i>View File
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <form action="<?= base_url('applicant/profile/files/' . $file['id'] . '/update') ?>" method="post" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label class="form-label">File Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="file_title" value="<?= old('file_title', $file['file_title']) ?>" required>
                                        <div class="invalid-feedback">
                                            Please provide a file title.
                                        </div>
                                        <div class="form-text">
                                            Enter a descriptive title for your document (e.g., "Resume", "Cover Letter", "Certificates")
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">File Description</label>
                                        <textarea class="form-control" name="file_description" rows="3" placeholder="Optional description of the document..."><?= old('file_description', $file['file_description']) ?></textarea>
                                        <div class="form-text">
                                            Provide additional details about this document (optional)
                                        </div>
                                    </div>

                                    <!-- Display extracted text info if available -->
                                    <?php if (!empty($file['file_extracted_texts'])): ?>
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-robot me-2"></i>AI Extracted Text
                                                    <span class="badge bg-success ms-2">Available</span>
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-text">
                                                    Text content has been successfully extracted from this document using AI.
                                                    This helps improve search functionality and application processing.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update File Information
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Additional Actions Card -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>Additional Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <a href="<?= base_url($file['file_path']) ?>" class="btn btn-outline-info w-100" target="_blank">
                                        <i class="fas fa-download me-2"></i>Download File
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-danger w-100" onclick="confirmDeleteFile(<?= $file['id'] ?>)">
                                        <i class="fas fa-trash me-2"></i>Delete File
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
});

function confirmDeleteFile(fileId) {
    if (confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('applicant/profile/files/') ?>' + fileId + '/delete';
        
        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);
        
        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?= $this->endSection() ?>
