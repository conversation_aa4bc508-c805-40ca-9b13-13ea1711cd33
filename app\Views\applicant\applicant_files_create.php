<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">Upload New File</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/profile#files') ?>">Profile</a></li>
                            <li class="breadcrumb-item active">Upload File</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>

            <!-- Upload Form Card -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>Upload Document
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Display validation errors -->
                            <?php if (session()->getFlashdata('errors')): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Display success message -->
                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('success') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Display error message -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <form action="<?= base_url('applicant/profile/files/store') ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label class="form-label">File Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="file_title" value="<?= old('file_title') ?>" required>
                                        <div class="invalid-feedback">
                                            Please provide a file title.
                                        </div>
                                        <div class="form-text">
                                            Enter a descriptive title for your document (e.g., "Resume", "Cover Letter", "Certificates")
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">File Description</label>
                                        <textarea class="form-control" name="file_description" rows="3" placeholder="Optional description of the document..."><?= old('file_description') ?></textarea>
                                        <div class="form-text">
                                            Provide additional details about this document (optional)
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">Select PDF File <span class="text-danger">*</span></label>
                                        <div class="custom-file-input-wrapper">
                                            <div class="custom-file-input" id="customFileInput">
                                                <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                                <p class="mb-2"><strong>Choose PDF Document</strong></p>
                                                <p class="text-muted small mb-3">Click to select a PDF file from your computer</p>
                                                <button type="button" class="btn btn-primary" id="selectFileBtn">
                                                    <i class="fas fa-folder-open me-2"></i>Browse Files
                                                </button>
                                                <input type="file" id="pdfFileInput" accept=".pdf" style="display: none;">
                                            </div>
                                            <div class="file-info mt-3" id="fileInfo" style="display: none;">
                                                <div class="alert alert-success">
                                                    <h6 class="alert-heading">
                                                        <i class="fas fa-check-circle me-2"></i>PDF File Selected
                                                    </h6>
                                                    <strong>File:</strong> <span id="fileName"></span><br>
                                                    <strong>Size:</strong> <span id="fileSize"></span><br>
                                                    <strong>Pages:</strong> <span id="totalPages"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>Only PDF files are supported. Maximum size: 25MB
                                        </div>
                                    </div>

                                    <!-- AI Processing Section -->
                                    <div class="col-12" id="aiProcessingSection" style="display: none;">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-robot me-2"></i>AI Text Extraction
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <button type="button" class="btn btn-primary" id="processBtn" onclick="processFile()">
                                                    <i class="fas fa-magic me-2"></i>Extract Text with AI
                                                </button>

                                                <!-- Progress Section -->
                                                <div class="progress-section mt-3" id="progressSection" style="display: none;">
                                                    <div class="status-text mb-2" id="statusText">Processing...</div>
                                                    <div class="progress mb-2">
                                                        <div class="progress-bar" id="progressFill" style="width: 0%"></div>
                                                    </div>
                                                    <div class="timing-info" id="timingInfo">
                                                        <small class="text-muted">
                                                            <strong>Elapsed:</strong> <span id="elapsedTime">0s</span> |
                                                            <strong>Status:</strong> <span id="currentStatus">Starting...</span>
                                                        </small>
                                                    </div>
                                                </div>

                                                <!-- Extracted Text Display -->
                                                <div class="extracted-text-section mt-3" id="extractedTextSection" style="display: none;">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-file-alt me-2"></i>Extracted Text
                                                        </h6>
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#extractedTextCollapse">
                                                            <i class="fas fa-eye me-1"></i>View Text
                                                        </button>
                                                    </div>
                                                    <div class="collapse" id="extractedTextCollapse">
                                                        <div class="card card-body bg-light">
                                                            <pre id="extractedTextContent" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-size: 12px;"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Final Upload Section -->
                                <div class="col-12" id="finalUploadSection" style="display: none;">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-check-circle me-2"></i>Ready to Upload
                                        </h6>
                                        <p class="mb-0">Text extraction completed successfully! You can now upload the file to your profile.</p>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="uploadFileBtn" style="display: none;">
                                        <i class="fas fa-upload me-2"></i>Upload File to Profile
                                    </button>
                                </div>

                                <!-- Hidden fields for extracted data -->
                                <input type="hidden" name="extracted_text" id="hiddenExtractedText">
                                <input type="hidden" name="file_data" id="hiddenFileData">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.custom-file-input-wrapper {
    margin-bottom: 1rem;
}

.custom-file-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.custom-file-input:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.progress-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
}

.timing-info {
    font-size: 14px;
}

.extracted-text-section {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// Gemini AI Configuration
const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
const GEMINI_MODEL = 'gemini-2.0-flash';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

let selectedFile = null;
let pdfDocument = null;
let totalPages = 0;
let extractedText = '';
let processingStartTime = null;
let timingInterval = null;

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

$(document).ready(function() {
    // File input handling - single event listener to prevent double dialogue
    document.getElementById('pdfFileInput').addEventListener('change', handleFileSelect);

    // Button click handling - prevent multiple event listeners
    document.getElementById('selectFileBtn').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('pdfFileInput').click();
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        event.preventDefault();

        if (!extractedText) {
            alert('Please process the PDF file first to extract text.');
            return;
        }

        // Set hidden fields
        document.getElementById('hiddenExtractedText').value = extractedText;
        document.getElementById('hiddenFileData').value = JSON.stringify({
            name: selectedFile.name,
            size: selectedFile.size,
            pages: totalPages
        });

        // Submit form normally
        this.submit();
    });
});

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
        handleFile(file);
    } else {
        alert('Please select a valid PDF file.');
    }
}

async function handleFile(file) {
    selectedFile = file;

    // Check file size (25MB = 25 * 1024 * 1024 bytes)
    const maxSize = 25 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('File size must be less than 25MB');
        return;
    }

    try {
        // Load PDF to get page count
        const arrayBuffer = await file.arrayBuffer();
        pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDocument.numPages;

        // Update UI
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('totalPages').textContent = totalPages;
        document.getElementById('fileInfo').style.display = 'block';
        document.getElementById('aiProcessingSection').style.display = 'block';

        showSuccess(`PDF loaded successfully! ${totalPages} pages found.`);
    } catch (error) {
        showError('Error loading PDF: ' + error.message);
    }
}

async function processFile() {
    if (!selectedFile || !pdfDocument) {
        alert('Please select a PDF file first.');
        return;
    }

    // Initialize timing
    processingStartTime = Date.now();
    extractedText = '';

    // Show progress section and update button
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('processBtn').disabled = true;
    document.getElementById('processBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

    // Start timing updates
    startTimingUpdates();

    try {
        const totalSets = Math.ceil(totalPages / 10);

        for (let setIndex = 0; setIndex < totalSets; setIndex++) {
            const startPage = setIndex * 10 + 1;
            const endPage = Math.min((setIndex + 1) * 10, totalPages);

            // Update progress
            const overallProgress = (setIndex / totalSets) * 100;
            document.getElementById('progressFill').style.width = overallProgress + '%';
            document.getElementById('statusText').textContent =
                `Processing set ${setIndex + 1} of ${totalSets} (Pages ${startPage}-${endPage})`;
            document.getElementById('currentStatus').textContent =
                `Extracting pages ${startPage}-${endPage}...`;

            // Extract pages for this set
            const pageImages = await extractPagesAsImages(startPage, endPage);

            // Send to Gemini AI
            const setText = await extractTextWithGemini(pageImages, startPage, endPage);

            // Append to full text
            if (setText) {
                extractedText += `\n\n## Pages ${startPage}-${endPage}\n\n${setText}`;
            }

            // Small delay to prevent rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Stop timing updates
        stopTimingUpdates();

        // Generate title and description using AI
        await generateTitleAndDescription();

        // Show final results
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('extractedTextContent').textContent = extractedText;
        document.getElementById('extractedTextSection').style.display = 'block';
        document.getElementById('finalUploadSection').style.display = 'block';
        document.getElementById('uploadFileBtn').style.display = 'inline-block';

        document.getElementById('processBtn').disabled = false;
        document.getElementById('processBtn').innerHTML = '<i class="fas fa-magic me-2"></i>Extract Text with AI';

        const totalTime = Date.now() - processingStartTime;
        showSuccess(`Text extraction completed successfully! Total time: ${formatTime(totalTime)}`);

    } catch (error) {
        stopTimingUpdates();
        showError('Error processing PDF: ' + error.message);
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('processBtn').disabled = false;
        document.getElementById('processBtn').innerHTML = '<i class="fas fa-magic me-2"></i>Extract Text with AI';
    }
}

async function extractPagesAsImages(startPage, endPage) {
    const images = [];

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        try {
            const page = await pdfDocument.getPage(pageNum);
            const scale = 2.0; // Higher scale for better text recognition
            const viewport = page.getViewport({ scale });

            // Create canvas
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render page to canvas
            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise;

            // Convert to base64
            const imageData = canvas.toDataURL('image/png').split(',')[1];
            images.push({
                pageNumber: pageNum,
                data: imageData
            });

        } catch (error) {
            console.error(`Error extracting page ${pageNum}:`, error);
        }
    }

    return images;
}

async function extractTextWithGemini(pageImages, startPage, endPage) {
    try {
        document.getElementById('currentStatus').textContent = 'Sending to Gemini AI...';

        // Prepare the parts for Gemini API
        const parts = [
            {
                text: `Please extract all text from these PDF pages (${startPage}-${endPage}) exactly as it appears.
                Do not modify, summarize, or interpret the content.
                Return the text in markdown format, preserving the original structure, formatting, and layout as much as possible.
                Include headers, paragraphs, lists, tables, and any other text elements.
                If there are multiple columns, preserve the column structure.
                Do not add any commentary or explanations - just return the extracted text.`
            }
        ];

        // Add each page image
        pageImages.forEach(image => {
            parts.push({
                inline_data: {
                    mime_type: "image/png",
                    data: image.data
                }
            });
        });

        const requestBody = {
            contents: [{
                parts: parts
            }],
            generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 8192
            }
        };

        document.getElementById('currentStatus').textContent = 'Waiting for Gemini response...';

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            return data.candidates[0].content.parts[0].text;
        } else {
            throw new Error('No text content returned from Gemini API');
        }

    } catch (error) {
        console.error(`Error processing pages ${startPage}-${endPage}:`, error);
        return `\n\n**Error processing pages ${startPage}-${endPage}: ${error.message}**\n\n`;
    }
}

async function generateTitleAndDescription() {
    try {
        document.getElementById('currentStatus').textContent = 'Generating title and description...';

        const prompt = `Based on the following extracted text from a PDF document, generate:
1. A brief, descriptive title (max 50 characters)
2. A concise description (max 200 characters) summarizing the document's content

Return your response in this exact format:
TITLE: [your title here]
DESCRIPTION: [your description here]

Extracted text:
${extractedText.substring(0, 2000)}...`;

        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.3,
                maxOutputTokens: 200
            }
        };

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (response.ok) {
            const data = await response.json();
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                const result = data.candidates[0].content.parts[0].text;

                // Parse the response
                const titleMatch = result.match(/TITLE:\s*(.+)/);
                const descMatch = result.match(/DESCRIPTION:\s*(.+)/);

                if (titleMatch) {
                    document.querySelector('input[name="file_title"]').value = titleMatch[1].trim();
                }
                if (descMatch) {
                    document.querySelector('textarea[name="file_description"]').value = descMatch[1].trim();
                }
            }
        }
    } catch (error) {
        console.error('Error generating title and description:', error);
        // Set default values
        document.querySelector('input[name="file_title"]').value = selectedFile.name.replace('.pdf', '');
        document.querySelector('textarea[name="file_description"]').value = 'PDF document with extracted text content';
    }
}

// Timing and utility functions
function formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

function startTimingUpdates() {
    timingInterval = setInterval(updateTimingDisplay, 1000);
}

function stopTimingUpdates() {
    if (timingInterval) {
        clearInterval(timingInterval);
        timingInterval = null;
    }
}

function updateTimingDisplay() {
    if (!processingStartTime) return;

    const elapsed = Date.now() - processingStartTime;
    document.getElementById('elapsedTime').textContent = formatTime(elapsed);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showError(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show';
    errorDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(errorDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show';
    successDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(successDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.remove();
        }
    }, 3000);
}
</script>
<?= $this->endSection() ?>
