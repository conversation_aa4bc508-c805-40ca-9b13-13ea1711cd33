<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">Upload New File</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/profile#files') ?>">Profile</a></li>
                            <li class="breadcrumb-item active">Upload File</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>

            <!-- Upload Form Card -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>Upload Document
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Display validation errors -->
                            <?php if (session()->getFlashdata('errors')): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Display success message -->
                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('success') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Display error message -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <form action="<?= base_url('applicant/profile/files/store') ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label class="form-label">File Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="file_title" value="<?= old('file_title') ?>" required>
                                        <div class="invalid-feedback">
                                            Please provide a file title.
                                        </div>
                                        <div class="form-text">
                                            Enter a descriptive title for your document (e.g., "Resume", "Cover Letter", "Certificates")
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">File Description</label>
                                        <textarea class="form-control" name="file_description" rows="3" placeholder="Optional description of the document..."><?= old('file_description') ?></textarea>
                                        <div class="form-text">
                                            Provide additional details about this document (optional)
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">Select File <span class="text-danger">*</span></label>
                                        <input type="file" class="form-control" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                                        <div class="invalid-feedback">
                                            Please select a file to upload.
                                        </div>
                                        <div class="form-text">
                                            Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG. Maximum size: 25MB
                                        </div>
                                    </div>

                                    <!-- File Upload Progress -->
                                    <div class="col-12">
                                        <div id="uploadProgress" class="d-none">
                                            <div class="alert alert-info">
                                                <div class="d-flex align-items-center">
                                                    <div class="spinner-border spinner-border-sm me-3" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <div>
                                                        <strong>Processing your file...</strong>
                                                        <div id="progressText">Uploading and extracting text content...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>Upload File
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show upload progress
            $('#uploadProgress').removeClass('d-none');
            
            // Disable submit button to prevent double submission
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
        }
        
        form.classList.add('was-validated');
    });

    // File input validation
    const fileInput = document.querySelector('input[type="file"]');
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (25MB = 25 * 1024 * 1024 bytes)
            const maxSize = 25 * 1024 * 1024;
            if (file.size > maxSize) {
                alert('File size must be less than 25MB');
                this.value = '';
                return;
            }
            
            // Check file type
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid file type (PDF, DOC, DOCX, JPG, JPEG, PNG)');
                this.value = '';
                return;
            }
        }
    });
});
</script>
<?= $this->endSection() ?>
